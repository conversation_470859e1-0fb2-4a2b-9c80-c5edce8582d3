# MCP Service

A gRPC-based microservice for MCP-Server Registery and management.

## Features

## Prerequisites

- Python 3.11 or higher
- Poetry (Python package manager)
- PostgreSQL database

## Setup

1. Install dependencies:

```bash
poetry install
```

2. Set up environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env

```

## Running the Service

Use the provided script:

```bash
chmod +x run_local.sh
./run_local.sh
```

Or run manually:

```bash
# Install dependencies
poetry install

# Initialize database
poetry run python -m app.db.init_db

# Start the service
poetry run python -m app.main
```

The service will start on port 50052.

## API Documentation

### gRPC Methods

## Development

### Project Structure

```
mcp-service/
├── app/
│   ├── api/
│   ├── core/          # Core functionality (config, security)
│   ├── db/            # Database models and session
│   ├── grpc/          # Generated gRPC code
│   ├── models/        # SQLAlchemy models
│   ├── schemas/       # Pydantic models
│   └── services/      # Business logic
├── proto-definitions/ # Proto files
├── tests/            # Test files
├── .env.example      # Example environment variables
├── poetry.lock       # Lock file for dependencies
├── pyproject.toml    # Project configuration
└── README.md         # This file
```

### Testing

Run tests with:

```bash
poetry run pytest
```

### Generating gRPC Code

After modifying proto files:

```bash
poetry run python -m app.scripts.generate_grpc
```
