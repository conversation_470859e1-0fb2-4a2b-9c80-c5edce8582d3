#!/usr/bin/env python3
"""
Simple migration script runner for MCP data migration.
This script runs the migration without requiring command-line parameters.
"""

import sys
import os

# Add the parent directory to the path to import app modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
sys.path.insert(0, root_dir)

from app.scripts.migrate_mcp_data import MCPDataMigrator
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Run the migration directly without command-line parameters."""
    print("🚀 Starting MCP Data Migration...")
    print("=" * 50)

    try:
        # First run a dry run to show what would be migrated
        print("📋 Running dry run first...")
        migrator = MCPDataMigrator()
        dry_run_success = migrator.run_migration(dry_run=True)

        if not dry_run_success:
            print("❌ Dry run failed! Please check the logs above.")
            return False

        print("\n" + "=" * 50)
        print("🔄 Now running actual migration...")

        # Run the actual migration
        actual_success = migrator.run_migration(dry_run=False)

        if actual_success:
            print("✅ Migration completed successfully!")
            print("\n📊 Summary:")
            print("- Old table: test-mcp-configs")
            print("- New table: mcp-configs")
            print("- URL transformation: url + url_type → urls JSON")
            print("- git_branch: Set to NULL")
            print("- deployment_status: Set to 'pending'")
            return True
        else:
            print("❌ Migration failed!")
            return False

    except Exception as e:
        logger.error(f"Fatal error during migration: {e}")
        print(f"❌ Fatal error: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
