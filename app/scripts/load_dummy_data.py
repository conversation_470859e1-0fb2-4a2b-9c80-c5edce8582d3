"""
Script to load dummy data into the MCP service database.
"""

import sys
import os

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.mcp_schema import McpConfig
from app.models.mcp_rating import McpRating
from app.utils.dummy_data.mcp_dummy_data import (
    MCP_CONFIG_DUMMY_DATA,
    MCP_RATING_DUMMY_DATA,
)


def load_mcp_configs(db: Session):
    """Load MCP config dummy data into the database."""
    print("Loading MCP configs...")
    for config_data in MCP_CONFIG_DUMMY_DATA:
        # Check if config already exists
        existing_config = db.query(McpConfig).filter(McpConfig.id == config_data["id"]).first()

        if existing_config:
            print(f"MCP config {config_data['name']} already exists, skipping.")
            continue

        # Create new config
        config = McpConfig(**config_data)
        db.add(config)
        print(f"Added MCP config: {config_data['name']}")

    db.commit()
    print("MCP configs loaded successfully.")


def load_mcp_ratings(db: Session):
    """Load MCP rating dummy data into the database."""
    print("Loading MCP ratings...")
    for rating_data in MCP_RATING_DUMMY_DATA:
        # Check if rating already exists
        existing_rating = db.query(McpRating).filter(McpRating.id == rating_data["id"]).first()

        if existing_rating:
            print(f"Rating {rating_data['id']} already exists, skipping.")
            continue

        # Create new rating
        rating = McpRating(**rating_data)
        db.add(rating)
        print(f"Added rating for MCP: {rating_data['mcp_id']}")

    db.commit()
    print("MCP ratings loaded successfully.")


def load_all_dummy_data():
    """Load all dummy data into the database."""
    db = SessionLocal()
    try:
        load_mcp_configs(db)
        load_mcp_ratings(db)
        print("All MCP service dummy data loaded successfully.")
    except Exception as e:
        print(f"Error loading dummy data: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    load_all_dummy_data()
