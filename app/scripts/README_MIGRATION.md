# MCP Data Migration Scripts

This directory contains scripts to migrate data from the old `test-mcp-configs` table to the new `mcp-configs` table.

## Overview

The migration transforms data from the old schema to the new schema with the following key changes:

- **URL Transformation**: Combines `url` and `url_type` fields into a JSON `urls` field
- **New Fields**: Adds `deployment_status` (set to 'pending') and `git_branch` (set to NULL)
- **Data Preservation**: All existing data is preserved and transformed appropriately

## Files

### 1. `migrate_mcp_data.py`
The main migration script with full functionality and command-line options.

**Features:**
- Dry run mode for testing
- Comprehensive error handling
- Detailed logging
- Data validation
- Conflict resolution (ON CONFLICT DO UPDATE)

**Usage:**
```bash
# Run with dry-run to see what would be migrated
poetry run python app/scripts/migrate_mcp_data.py --dry-run

# Run actual migration
poetry run python app/scripts/migrate_mcp_data.py
```

### 2. `run_mcp_migration.py`
A simplified runner script that executes the migration without command-line parameters.

**Features:**
- Runs dry-run first, then actual migration
- No command-line parameters needed
- User-friendly output

**Usage:**
```bash
poetry run python app/scripts/run_mcp_migration.py
```

## Data Transformation Details

### URL Field Transformation
**Old Schema:**
```sql
url VARCHAR
url_type VARCHAR
```

**New Schema:**
```sql
urls JSON
```

**Transformation:**
```json
// If old record has url="https://example.com" and url_type="http"
// New record will have:
{
  "urls": [{"url": "https://example.com", "type": "http"}]
}
```

### New Fields
- `deployment_status`: Set to `'pending'` for all migrated records
- `git_branch`: Set to `NULL` for all migrated records (as requested)

### Preserved Fields
All other fields from the old table are preserved as-is:
- `id`, `name`, `description`, `logo`
- `mcp_tools_config`, `visibility`, `owner_id`, `owner_type`
- `user_ids`, `organization_user_ids`, `use_count`, `average_rating`
- `department`, `tags`, `status`, `git_url`
- `created_at`, `updated_at`

## Prerequisites

1. **Database Setup**: Ensure the new `mcp-configs` table exists
2. **Environment**: Make sure database connection settings are configured
3. **Dependencies**: Install required Python packages via Poetry

## Safety Features

1. **Table Existence Checks**: Verifies both old and new tables exist
2. **Dry Run Mode**: Test the migration without making changes
3. **Conflict Resolution**: Uses `ON CONFLICT DO UPDATE` to handle duplicates
4. **Error Handling**: Comprehensive error handling with detailed logging
5. **Transaction Safety**: Uses database transactions for data integrity

## Running the Migration

### Option 1: Simple Run (Recommended)
```bash
cd /path/to/mcp-service
poetry run python app/scripts/run_mcp_migration.py
```

### Option 2: Advanced Run with Options
```bash
# Test first with dry run
poetry run python app/scripts/migrate_mcp_data.py --dry-run

# Run actual migration
poetry run python app/scripts/migrate_mcp_data.py
```

## Troubleshooting

### Common Issues

1. **Table doesn't exist**: Ensure database initialization has been run
2. **Connection errors**: Check database configuration in settings
3. **Permission errors**: Ensure database user has appropriate permissions

### Logs
The scripts provide detailed logging. Check the console output for:
- Number of records found in old table
- Transformation progress
- Insert results
- Any errors or warnings

## Post-Migration

After successful migration:
1. Verify data in the new `mcp-configs` table
2. Test application functionality with new schema
3. Consider backing up or archiving the old `test-mcp-configs` table
4. Update any application code that references the old table structure
