#!/usr/bin/env python3
"""
Migration script to transfer data from test-mcp-configs to mcp-configs table.
This script transforms the old url/url_type fields into the new urls JSON format.
"""

import json
import logging
import sys
import os
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
root_dir = os.path.dirname(parent_dir)
sys.path.insert(0, root_dir)

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.utils.constants.constants import DeploymentStatus

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MCPDataMigrator:
    def __init__(self):
        """Initialize the migrator with database connection."""
        self.engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

    def check_old_table_exists(self) -> bool:
        """Check if the old test-mcp-configs table exists."""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'test-mcp-configs'
                    );
                """))
                exists = result.scalar()
                logger.info(f"Old table 'test-mcp-configs' exists: {exists}")
                return exists
        except Exception as e:
            logger.error(f"Error checking old table existence: {e}")
            return False

    def check_new_table_exists(self) -> bool:
        """Check if the new mcp-configs table exists."""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'mcp-configs'
                    );
                """))
                exists = result.scalar()
                logger.info(f"New table 'mcp-configs' exists: {exists}")
                return exists
        except Exception as e:
            logger.error(f"Error checking new table existence: {e}")
            return False

    def get_old_table_data(self) -> List[Dict[str, Any]]:
        """Fetch all data from the old test-mcp-configs table."""
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text("""
                    SELECT * FROM "test-mcp-configs"
                """))

                # Convert result to list of dictionaries
                columns = result.keys()
                data = [dict(zip(columns, row)) for row in result.fetchall()]

                logger.info(f"Retrieved {len(data)} records from test-mcp-configs table")
                return data

        except Exception as e:
            logger.error(f"Error fetching data from old table: {e}")
            raise

    def transform_url_data(self, url: Optional[str], url_type: Optional[str]) -> Optional[str]:
        """Transform old url/url_type fields into new urls JSON format."""
        if not url or not url_type:
            return None

        urls_list = [{"url": url, "type": url_type}]
        return json.dumps(urls_list)

    def map_old_to_new_schema(self, old_record: Dict[str, Any]) -> Dict[str, Any]:
        """Map old table fields to new table schema."""
        # Transform URLs
        urls_json = self.transform_url_data(
            old_record.get('url'),
            old_record.get('url_type')
        )

        # Handle mcp_tools_config - convert to JSON string if it's a dict
        mcp_tools_config = old_record.get('mcp_tools_config')
        if isinstance(mcp_tools_config, dict):
            mcp_tools_config = json.dumps(mcp_tools_config)

        # Map the record to new schema
        new_record = {
            'id': old_record.get('id'),
            'name': old_record.get('name'),
            'description': old_record.get('description'),
            'logo': old_record.get('logo'),
            'mcp_tools_config': mcp_tools_config,
            'visibility': old_record.get('visibility'),
            'owner_id': old_record.get('owner_id'),
            'owner_type': old_record.get('owner_type'),
            'user_ids': old_record.get('user_ids'),
            'organization_user_ids': old_record.get('organization_user_ids'),
            'use_count': old_record.get('use_count', 0),
            'average_rating': old_record.get('average_rating', 0.0),
            'department': old_record.get('department'),
            'tags': old_record.get('tags'),
            'status': old_record.get('status'),
            'urls': urls_json,  # Transformed URLs
            'deployment_status': None,  # Set to NULL for migrated records
            'git_url': old_record.get('git_url'),
            'git_branch': None,  # Set to NULL as requested
            'created_at': old_record.get('created_at'),
            'updated_at': old_record.get('updated_at')
        }

        return new_record

    def insert_records_to_new_table(self, records: List[Dict[str, Any]], dry_run: bool = False) -> bool:
        """Insert transformed records into the new mcp-configs table."""
        if not records:
            logger.info("No records to insert")
            return True

        if dry_run:
            logger.info(f"DRY RUN: Would insert {len(records)} records")
            for i, record in enumerate(records[:3]):  # Show first 3 records as example
                logger.info(f"Sample record {i+1}: {record}")
            return True

        try:
            with self.engine.connect() as connection:
                # Prepare the insert query
                insert_query = text("""
                    INSERT INTO "mcp-configs" (
                        id, name, description, logo, mcp_tools_config, visibility,
                        owner_id, owner_type, user_ids, organization_user_ids,
                        use_count, average_rating, department, tags, status,
                        urls, deployment_status, git_url, git_branch,
                        created_at, updated_at
                    ) VALUES (
                        :id, :name, :description, :logo, :mcp_tools_config, :visibility,
                        :owner_id, :owner_type, :user_ids, :organization_user_ids,
                        :use_count, :average_rating, :department, :tags, :status,
                        :urls, :deployment_status, :git_url, :git_branch,
                        :created_at, :updated_at
                    )
                    ON CONFLICT (id) DO UPDATE SET
                        name = EXCLUDED.name,
                        description = EXCLUDED.description,
                        logo = EXCLUDED.logo,
                        mcp_tools_config = EXCLUDED.mcp_tools_config,
                        visibility = EXCLUDED.visibility,
                        owner_id = EXCLUDED.owner_id,
                        owner_type = EXCLUDED.owner_type,
                        user_ids = EXCLUDED.user_ids,
                        organization_user_ids = EXCLUDED.organization_user_ids,
                        use_count = EXCLUDED.use_count,
                        average_rating = EXCLUDED.average_rating,
                        department = EXCLUDED.department,
                        tags = EXCLUDED.tags,
                        status = EXCLUDED.status,
                        urls = EXCLUDED.urls,
                        deployment_status = EXCLUDED.deployment_status,
                        git_url = EXCLUDED.git_url,
                        git_branch = EXCLUDED.git_branch,
                        updated_at = EXCLUDED.updated_at
                """)

                # Execute the insert for all records
                connection.execute(insert_query, records)
                connection.commit()

                logger.info(f"Successfully inserted {len(records)} records into mcp-configs table")
                return True

        except Exception as e:
            logger.error(f"Error inserting records: {e}")
            raise

    def run_migration(self, dry_run: bool = False) -> bool:
        """Run the complete migration process."""
        try:
            logger.info("Starting MCP data migration...")

            # Check if new table exists
            if not self.check_new_table_exists():
                logger.error("New table 'mcp-configs' does not exist! Please run database initialization first.")
                return False

            # Check if old table exists
            if not self.check_old_table_exists():
                logger.info("Old table 'test-mcp-configs' does not exist. Nothing to migrate.")
                return True

            # Fetch data from old table
            old_data = self.get_old_table_data()
            if not old_data:
                logger.info("No data found in old table")
                return True

            # Transform data
            logger.info("Transforming data...")
            transformed_records = []
            for record in old_data:
                try:
                    new_record = self.map_old_to_new_schema(record)
                    transformed_records.append(new_record)
                except Exception as e:
                    logger.error(f"Error transforming record {record.get('id', 'unknown')}: {e}")
                    continue

            logger.info(f"Successfully transformed {len(transformed_records)} out of {len(old_data)} records")

            # Insert transformed data
            success = self.insert_records_to_new_table(transformed_records, dry_run)

            if success:
                if dry_run:
                    logger.info("Migration dry run completed successfully!")
                else:
                    logger.info("Migration completed successfully!")

            return success

        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False


def main():
    """Main function to run the migration."""
    import argparse

    parser = argparse.ArgumentParser(description='Migrate MCP data from old to new table')
    parser.add_argument('--dry-run', action='store_true',
                       help='Run in dry-run mode (no actual changes)')

    args = parser.parse_args()

    try:
        migrator = MCPDataMigrator()
        success = migrator.run_migration(dry_run=args.dry_run)

        if success:
            if args.dry_run:
                print("✅ Dry run completed successfully!")
            else:
                print("✅ Migration completed successfully!")
            sys.exit(0)
        else:
            print("❌ Migration failed!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
