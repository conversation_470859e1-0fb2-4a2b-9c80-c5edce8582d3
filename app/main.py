import os
import grpc
from concurrent import futures

from app.services.mcp_service import MCPConfigService
from app.grpc import mcp_pb2_grpc


def serve():
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

    # Add MCP service to server
    mcp_service = MCPConfigService()
    mcp_pb2_grpc.add_MCPServiceServicer_to_server(mcp_service, server)

    # Get port from environment or use default
    port = os.getenv('PORT', '50058') 
    server.add_insecure_port(f'[::]:{port}')

    # Start server
    server.start()
    print(f"MCP service started on port {port}")

    # Keep thread alive
    server.wait_for_termination()

if __name__ == '__main__':
    serve()