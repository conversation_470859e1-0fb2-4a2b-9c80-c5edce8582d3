import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Float
from sqlalchemy.orm import declarative_base
from sqlalchemy.dialects.postgresql import UUID
from app.utils.constants.table_names import MCP_RATING_TABLE

Base = declarative_base()


class McpRating(Base):
    __tablename__ = MCP_RATING_TABLE

    # Primary key as UUID
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # MCP ID (removed foreign key constraint)
    mcp_id = Column(String, nullable=False)

    # User who provided the rating
    user_id = Column(String, nullable=False)

    # Rating value (1.0 to 5.0)
    rating = Column(Float, nullable=False)

    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        nullable=False,
    )

    def __repr__(self):
        return f"<McpRating(id={self.id}, mcp_id='{self.mcp_id}', user_id='{self.user_id}', rating={self.rating})>"
