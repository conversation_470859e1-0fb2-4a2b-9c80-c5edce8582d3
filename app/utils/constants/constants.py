from enum import Enum


class McpVisibility(str, Enum):
    PRIVATE = "private"
    PUBLIC = "public"


class McpStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    DRAFT = "draft"


class McpOwnerType(str, Enum):
    USER = "user"
    ENTERPRISE = "enterprise"
    PLATFORM = "platform"


class McpDepartment(str, Enum):
    GENERAL = "general"
    SALES = "sales"
    MARKETING = "marketing"
    ENGINEERING = "engineering"
    FINANCE = "finance"


class UrlType(str, Enum):
    SSE = "sse"
    HTTP = "http"

class DeploymentStatus(str, Enum):
    PENDING = "pending"
    COMPLETED = "completed"


class MarketplaceItemSortEnum(str, Enum):
    NEWEST = "newest"
    OLDEST = "oldest"
    MOST_POPULAR = "most_popular"
    HIGHEST_RATED = "highest_rated"
