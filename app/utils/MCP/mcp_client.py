import asyncio
import sys
from typing import Any, Optional
from urllib.parse import urlparse
import logging

import mcp.types as types
from mcp.client.session import ClientSession
from mcp.client.sse import sse_client

logger = logging.getLogger(__name__)


class MCPClient:

    def __init__(self, server_url: str):
        parsed = urlparse(server_url)
        if parsed.scheme not in ("http", "https"):
            raise ValueError("Server URL must start with http:// or https://")
        self.server_url = server_url
        self._sse_context: Optional[sse_client] = None
        self._session: Optional[ClientSession] = None
        self._streams: Optional[tuple] = None

    async def __aenter__(self):
        self._sse_context = sse_client(self.server_url)
        self._streams = await self._sse_context.__aenter__()
        self._session = ClientSession(self._streams[0], self._streams[1])
        await self._session.__aenter__()
        await self._session.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    async def close(self):
        if self._session is not None:
            await self._session.__aexit__(None, None, None)
            self._session = None
        if self._sse_context is not None:
            await self._sse_context.__aexit__(None, None, None)
            self._sse_context = None
        self._streams = None

    async def _ensure_connected(self):
        if self._session is None or self._sse_context is None:
            raise RuntimeError("Client is not connected")

    async def list_resources(self) -> list[types.Resource]:
        await self._ensure_connected()
        return await self._session.list_resources()  # type: ignore

    async def read_resource(
        self, resource_uri: str
    ) -> list[types.TextResourceContents | types.BlobResourceContents]:
        await self._ensure_connected()
        return await self._session.read_resource(resource_uri)  # type: ignore

    async def list_tools(self) -> list[types.Tool]:
        await self._ensure_connected()
        return await self._session.list_tools()  # type: ignore

    async def call_tool(
        self, tool_name: str, arguments: dict[str, Any]
    ) -> types.CallToolResult:
        await self._ensure_connected()
        return await self._session.call_tool(tool_name, arguments)  # type: ignore

    async def list_prompts(self) -> list[types.Prompt]:
        await self._ensure_connected()
        return await self._session.list_prompts()  # type: ignore

    async def get_prompt(
        self, prompt_name: str, arguments: dict[str, Any] = None
    ) -> list[types.PromptMessage]:
        await self._ensure_connected()
        return await self._session.get_prompt(prompt_name, arguments)  # type: ignore

    async def subscribe_resource(self, resource_uri: str):
        await self._ensure_connected()
        return await self._session.subscribe_resource(resource_uri)  # type: ignore

    async def unsubscribe_resource(self, resource_uri: str):
        await self._ensure_connected()
        return await self._session.unsubscribe_resource(resource_uri)  # type: ignore


# how to use
async def main(server_url: str):
    try:
        client = MCPClient(server_url)
        await client.__aenter__()
        logger.debug(f"Connected to MCP server at {server_url}")

        resources = await client.list_resources()
        logger.debug(f"Resources: , {resources}")

        tools = await client.list_tools()
        logger.debug(f"Tools:{tools}")

        prompts = await client.list_prompts()
        logger.debug(f"Prompts: {prompts}")

        await client.close()

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    if len(sys.argv) != 2:
        logger.debug("Usage: uv run client.py <server_url>")
        logger.debug("Example: uv run client.py http://localhost:8080/sse")
        sys.exit(1)

    asyncio.run(main(sys.argv[1]))